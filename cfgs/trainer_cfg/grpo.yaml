defaults:
  - base
  - _self_

trainer_log_name: grpo

artificial_epochs: 1

max_steps: 200
train_batch_size: 252
per_device_train_batch_size: 3

num_generations: null

learning_rate: 0.000001
beta: 0.04

model_init_kwargs: null
remove_unused_columns: false

max_prompt_length: 2048
max_completion_length: 16384

shuffle_generation_inputs: true

ds3_gather_for_generation: true

temperature: 1.0
top_p: 1.0
top_k: ~
min_p: ~
repetition_penalty: 1.0

generation_aggregation_steps: null

use_vllm: true
vllm_device: auto
vllm_gpu_memory_utilization: 0.9
vllm_dtype: auto
vllm_max_model_len: null

use_ray: false
ray_share_training_devices: false
ray_tensor_parallelism: 1
ray_data_parallelism: null
ray_no_memory_duplication: false
vllm_sleep_level: 0
enable_prefix_caching: false
enforce_eager: true

use_vllm_server: false
vllm_host: null
vllm_port: null
vllm_group_port: 51216
num_vllm_clients: 1

reward_weights: null

sync_ref_model: false
ref_model_mixup_alpha: 0.9
ref_model_sync_steps: 64

backprop_accumulation_steps: null
backprop_accumulation_micro_batch_size: null
offload_untrained_models: false
unbias_log_probabilities: true

log_completions: false
save_completions_probability: ~

push_to_hub: false

activate_debugging_logs: false

trainer_args:
  _target_: trainers.GRPOConfig
  model_init_kwargs: ${model_init_kwargs}
  remove_unused_columns: ${remove_unused_columns}
  max_prompt_length: ${max_prompt_length}
  num_generations: ${num_generations}
  max_completion_length: ${max_completion_length}
  shuffle_generation_inputs: ${shuffle_generation_inputs}
  ds3_gather_for_generation: ${ds3_gather_for_generation}
  temperature: ${temperature}
  top_p: ${top_p}
  top_k: ${top_k}
  min_p: ${min_p}
  repetition_penalty: ${repetition_penalty}
  generation_aggregation_steps: ${generation_aggregation_steps}
  use_vllm: ${use_vllm}
  vllm_device: ${vllm_device}
  vllm_gpu_memory_utilization: ${vllm_gpu_memory_utilization}
  vllm_dtype: ${vllm_dtype}
  vllm_max_model_len: ${vllm_max_model_len}
  use_ray: ${use_ray}
  ray_share_training_devices: ${ray_share_training_devices}
  ray_tensor_parallelism: ${ray_tensor_parallelism}
  ray_data_parallelism: ${ray_data_parallelism}
  ray_no_memory_duplication: ${ray_no_memory_duplication}
  enable_prefix_caching: ${enable_prefix_caching}
  enforce_eager: ${enforce_eager}
  vllm_sleep_level: ${vllm_sleep_level}
  use_vllm_server: ${use_vllm_server}
  vllm_host: ${vllm_host}
  vllm_port: ${vllm_port}
  vllm_group_port: ${vllm_group_port}
  num_vllm_clients: ${num_vllm_clients}
  learning_rate: ${learning_rate}
  beta: ${beta}
  reward_weights: ${reward_weights}
  sync_ref_model: ${sync_ref_model}
  ref_model_mixup_alpha: ${ref_model_mixup_alpha}
  ref_model_sync_steps: ${ref_model_sync_steps}
  log_completions: ${log_completions}
  save_completions_probability: ${save_completions_probability}
  artificial_epochs: ${artificial_epochs}
  backprop_accumulation_steps: ${backprop_accumulation_steps}
  backprop_accumulation_micro_batch_size: ${backprop_accumulation_micro_batch_size}
  offload_untrained_models: ${offload_untrained_models}
  unbias_log_probabilities: ${unbias_log_probabilities}
  activate_debugging_logs: ${activate_debugging_logs}
  push_to_hub: ${push_to_hub}

trainer:
  _target_: trainers.GRPOTrainer
  reward_funcs: ${reward_fns}
