defaults:
  - override /model_cfg@_global_: qwen7bi
  - override /data_cfg@_global_: teacher_bespoke_stratos
  - override /trainer_cfg@_global_: teacher_grpo
  - _self_

model_name_or_path: results/pre_rl_model

bf16: true

max_steps: 125
num_train_epochs: 1.0

save_strategy: "steps"

save_steps: 25
do_eval: true
save_final_model: true

eval_strategy: steps
eval_steps: 4

logging_steps: 1
logging_strategy: steps

train_batch_size: 1024
per_device_train_batch_size: 1
generation_aggregation_steps: 256

num_generations: 64
temperature: 0.7
unbias_log_probabilities: true

learning_rate: 0.000001
beta: 0.04

sync_ref_model: true
ref_model_mixup_alpha: 0.9
ref_model_sync_steps: 32

student_model: "bespokelabs/Bespoke-Stratos-7B"
student_model_init_kwargs: null

offload_untrained_models: true

max_prompt_length: 16384
max_completion_length: 16384

answer_log_prob_coeff: [1, 0.01]
kl_penalty_reward_coeff: [3, 0.03]
normalize_log_prob_fn: null
clip_log_prob: 100000
normalize_kl_fn: null
clip_kl: 100000
reduction_log_prob_fn: ["mean", "min"]
reduction_kl_fn: ["mean", "max"]
use_schulman_kl_estimation: false
not_matched_penalty: -1.0
unbias_teacher_log_probs: true
unbias_student_log_probs_temp: 0.7

save_completions_probability: 0.1

lr_scheduler_type: "constant"
lr_scheduler_kwargs: ~

wandb_project: rl4lm_teacher

log_ctx_name: gr${num_generations}_${max_prompt_length}ctx_${max_completion_length}gen

wandb_run_name: ${trainer_log_name}

output_dir: ${results_dir}/rlt_teacher/${exp_name}

vllm_port: 8765
use_vllm: false
use_vllm_server: true
vllm_host:
num_vllm_clients:
